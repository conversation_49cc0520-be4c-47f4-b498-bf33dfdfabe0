"""
Test script to verify duplicate prevention works correctly
"""

import sys
from datetime import datetime, timedelta
from database import get_db_session, init_database
from models import Company, IPO
from data_collectors import IPODataManager, IPOData

def create_test_ipo_data():
    """Create test IPO data for duplicate testing"""
    return [
        IPOData(
            company_name="TestCorp Alpha [TEST]",
            ticker_symbol="TSTA",
            industry="Technology",
            sector="Software",
            ceo_name="John Test",
            headquarters="San Francisco, CA",
            description="Test company for duplicate prevention testing",
            filing_date=datetime.utcnow() - timedelta(days=1),
            exchange="NASDAQ",
            sec_filing_url="https://test.sec.gov/filing1",
            cik_number="**********"
        ),
        IPOData(
            company_name="TestCorp Beta [TEST]",
            ticker_symbol="TSTB",
            industry="Healthcare",
            sector="Biotechnology",
            ceo_name="Jane Test",
            headquarters="Boston, MA",
            description="Another test company for duplicate testing",
            filing_date=datetime.utcnow() - timedelta(days=2),
            exchange="NYSE",
            sec_filing_url="https://test.sec.gov/filing2",
            cik_number="**********"
        )
    ]

def test_initial_save():
    """Test initial save of IPO data"""
    print("🧪 Testing initial IPO data save...")
    
    try:
        # Clean database first
        with get_db_session() as session:
            session.query(IPO).delete()
            session.query(Company).delete()
            session.commit()
        
        # Create test data
        test_data = create_test_ipo_data()
        data_manager = IPODataManager()
        
        saved_count = 0
        for ipo_data in test_data:
            if data_manager._save_ipo_to_database(ipo_data):
                saved_count += 1
        
        print(f"✅ Saved {saved_count} companies initially")
        
        # Verify data was saved
        with get_db_session() as session:
            company_count = session.query(Company).count()
            ipo_count = session.query(IPO).count()
            
            print(f"   Database contains: {company_count} companies, {ipo_count} IPOs")
            
            if company_count == len(test_data) and ipo_count == len(test_data):
                print("✅ Initial save test passed")
                return True
            else:
                print("❌ Initial save test failed")
                return False
                
    except Exception as e:
        print(f"❌ Initial save test failed: {e}")
        return False

def test_duplicate_prevention():
    """Test that duplicates are prevented"""
    print("\n🧪 Testing duplicate prevention...")
    
    try:
        # Try to save the same data again
        test_data = create_test_ipo_data()
        data_manager = IPODataManager()
        
        saved_count = 0
        for ipo_data in test_data:
            if data_manager._save_ipo_to_database(ipo_data):
                saved_count += 1
        
        print(f"   Attempted to save {len(test_data)} duplicates, actually saved: {saved_count}")
        
        # Verify no duplicates were added
        with get_db_session() as session:
            company_count = session.query(Company).count()
            ipo_count = session.query(IPO).count()
            
            print(f"   Database still contains: {company_count} companies, {ipo_count} IPOs")
            
            if company_count == len(test_data) and ipo_count == len(test_data) and saved_count == 0:
                print("✅ Duplicate prevention test passed")
                return True
            else:
                print("❌ Duplicate prevention test failed - duplicates were added!")
                return False
                
    except Exception as e:
        print(f"❌ Duplicate prevention test failed: {e}")
        return False

def test_update_existing():
    """Test updating existing companies with new information"""
    print("\n🧪 Testing update of existing companies...")
    
    try:
        # Create updated data with additional information
        updated_data = IPOData(
            company_name="TestCorp Alpha [TEST]",  # Same name
            ticker_symbol="TSTA",  # Same ticker
            industry="Technology",  # Same
            sector="Software",  # Same
            ceo_name="John Test Updated",  # Updated
            headquarters="San Francisco, CA",  # Same
            description="Updated description for test company",  # Updated
            filing_date=datetime.utcnow() - timedelta(days=1),
            exchange="NASDAQ",
            sec_filing_url="https://test.sec.gov/filing1-updated",  # Updated
            cik_number="**********"  # Same
        )
        
        data_manager = IPODataManager()
        was_updated = data_manager._save_ipo_to_database(updated_data)
        
        if was_updated:
            print("✅ Existing company was updated with new information")
            
            # Verify the update
            with get_db_session() as session:
                company = session.query(Company).filter(
                    Company.name == "TestCorp Alpha [TEST]"
                ).first()
                
                if company and "Updated" in company.ceo_name:
                    print("✅ Update verification passed")
                    return True
                else:
                    print("❌ Update verification failed")
                    return False
        else:
            print("ℹ️  No update was needed (this is also valid)")
            return True
            
    except Exception as e:
        print(f"❌ Update test failed: {e}")
        return False

def test_different_duplicate_checks():
    """Test duplicate detection by different fields"""
    print("\n🧪 Testing duplicate detection by different fields...")
    
    try:
        data_manager = IPODataManager()
        
        # Test duplicate by CIK number (different name)
        duplicate_by_cik = IPOData(
            company_name="Different Name Corp [TEST]",
            cik_number="**********",  # Same CIK as existing
            ticker_symbol="DIFF",
            filing_date=datetime.utcnow(),
            exchange="NASDAQ"
        )
        
        saved = data_manager._save_ipo_to_database(duplicate_by_cik)
        if not saved:
            print("✅ Duplicate detection by CIK works")
        else:
            print("❌ Duplicate detection by CIK failed")
            return False
        
        # Test duplicate by ticker (different name and CIK)
        duplicate_by_ticker = IPOData(
            company_name="Another Different Corp [TEST]",
            cik_number="0009998999",  # Different CIK
            ticker_symbol="TSTA",  # Same ticker as existing
            filing_date=datetime.utcnow(),
            exchange="NYSE"
        )
        
        saved = data_manager._save_ipo_to_database(duplicate_by_ticker)
        if not saved:
            print("✅ Duplicate detection by ticker works")
            return True
        else:
            print("❌ Duplicate detection by ticker failed")
            return False
            
    except Exception as e:
        print(f"❌ Different duplicate checks test failed: {e}")
        return False

def main():
    """Run all duplicate prevention tests"""
    print("🚀 DUPLICATE PREVENTION TEST SUITE")
    print("=" * 60)
    
    # Initialize database
    try:
        init_database()
        print("✅ Database initialized")
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False
    
    tests = [
        ("Initial Save", test_initial_save),
        ("Duplicate Prevention", test_duplicate_prevention),
        ("Update Existing", test_update_existing),
        ("Different Duplicate Checks", test_different_duplicate_checks),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: CRASHED - {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 All duplicate prevention tests passed!")
        print("\nYour system correctly:")
        print("✅ Prevents duplicate companies by name")
        print("✅ Prevents duplicate companies by CIK number")
        print("✅ Prevents duplicate companies by ticker symbol")
        print("✅ Updates existing companies with new information")
        print("✅ Maintains data integrity")
    else:
        print(f"\n⚠️  {len(tests) - passed} test(s) failed")
    
    # Clean up test data
    try:
        with get_db_session() as session:
            session.query(IPO).filter(IPO.sec_filing_url.like('%test.sec.gov%')).delete()
            session.query(Company).filter(Company.name.like('%[TEST]%')).delete()
            session.commit()
        print("\n🧹 Test data cleaned up")
    except Exception as e:
        print(f"\n⚠️  Failed to clean up test data: {e}")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
