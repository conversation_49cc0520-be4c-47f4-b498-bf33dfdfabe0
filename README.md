# IPO Tracker

An automated system that tracks new IPO filings on NASDAQ and NYSE, stores company information in a PostgreSQL database, and generates AI-powered business analysis reports.

## 🚀 Quick Start

### Prerequisites

1. **Python 3.8+** - Check with `python --version`
2. **PostgreSQL** - Database server
   - macOS: `brew install postgresql`
   - Ubuntu: `sudo apt-get install postgresql postgresql-contrib`
   - Windows: Download from [postgresql.org](https://www.postgresql.org/download/)

### Installation

1. **Clone or download this project** to your computer

2. **Run the automated setup:**
   ```bash
   python setup.py
   ```
   
   This will:
   - Create a virtual environment
   - Install all required packages
   - Set up configuration files
   - Create the PostgreSQL database
   - Guide you through the setup process

3. **Get your API keys:**
   - **OpenAI API Key**: Go to [OpenAI API Keys](https://platform.openai.com/api-keys)
   - **Alpha Vantage Key**: Go to [Alpha Vantage](https://www.alphavantage.co/support/#api-key) (free)

4. **Edit the `.env` file** with your API keys:
   ```
   OPENAI_API_KEY=your_actual_openai_key_here
   ALPHA_VANTAGE_API_KEY=your_actual_alpha_vantage_key_here
   ```

5. **Initialize the database:**
   ```bash
   python database.py
   ```

6. **Start the application:**
   ```bash
   python main.py
   ```

## 📁 Project Structure

```
IPO_Tracker/
├── config.py          # Configuration and settings
├── models.py          # Database table definitions
├── database.py        # Database management
├── main.py            # Main application
├── setup.py           # Automated setup script
├── requirements.txt   # Python dependencies
├── .env.example       # Environment variables template
├── .env              # Your actual environment variables (created during setup)
├── logs/             # Application logs
└── data/             # Data storage directory
```

## 🗄️ Database Schema

The system uses PostgreSQL with these main tables:

- **companies**: Basic company information (name, industry, CEO, etc.)
- **ipos**: IPO-specific data (filing dates, price ranges, status)
- **ai_reports**: AI-generated business analysis reports
- **data_sources**: Tracking of data source updates and rate limits

## 🔧 Configuration

Key settings in `.env` file:

```bash
# Database
DATABASE_URL=postgresql://username:password@localhost:5432/ipo_tracker

# API Keys
OPENAI_API_KEY=your_openai_api_key
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key

# Settings
LOG_LEVEL=INFO
SCHEDULER_INTERVAL_HOURS=6
```

## 🚦 Current Status

This is the **foundation phase** of the project. Currently implemented:

✅ **Database Schema**: Complete PostgreSQL schema for companies, IPOs, and AI reports  
✅ **Configuration System**: Environment-based configuration with validation  
✅ **Database Management**: Connection handling, session management, and initialization  
✅ **Logging System**: Comprehensive logging to files and console  
✅ **Setup Automation**: Automated installation and configuration  

## 🔄 Next Steps

The following components will be built next:

1. **IPO Data Collection**: Automated fetching from SEC EDGAR and financial APIs
2. **AI Report Generation**: OpenAI integration for business analysis
3. **Background Scheduler**: Automated monitoring and updates
4. **Web Interface**: User-friendly dashboard for viewing data
5. **Notifications**: Email/desktop alerts for new IPOs

## 🛠️ Troubleshooting

### Database Connection Issues
```bash
# Test PostgreSQL connection
psql -U postgres -d ipo_tracker -c "SELECT 1;"

# Check if PostgreSQL is running
# macOS: brew services list | grep postgresql
# Ubuntu: sudo systemctl status postgresql
```

### Python Environment Issues
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv

# Activate and reinstall
# macOS/Linux: source venv/bin/activate
# Windows: venv\Scripts\activate
pip install -r requirements.txt
```

### API Key Issues
- Make sure your `.env` file has the correct API keys
- Test OpenAI key at [OpenAI Playground](https://platform.openai.com/playground)
- Verify Alpha Vantage key with a test request

## 📞 Support

If you encounter any issues:

1. Check the logs in the `logs/` directory
2. Run `python main.py` and select "Test Database Connection"
3. Verify your `.env` file has all required values
4. Make sure PostgreSQL is running and accessible

## 🎯 Goals

This system will automatically:
- Monitor SEC filings for new IPO registrations
- Extract company information from filing documents
- Generate comprehensive AI-powered business analysis reports
- Provide a clean interface to browse and search IPO data
- Send notifications when interesting IPOs are detected

---

**Note**: This is an educational/personal project. Always verify information from official sources before making investment decisions.
