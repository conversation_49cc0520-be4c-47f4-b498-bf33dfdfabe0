"""
Database models for the IPO Tracker application.
These define the structure of our database tables.
"""

from datetime import datetime
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Float, Boolean, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from config import Config

# Create the base class for our models
Base = declarative_base()

class Company(Base):
    """
    Stores basic information about companies that have filed for IPO
    """
    __tablename__ = 'companies'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(255), nullable=False)
    ticker_symbol = Column(String(10), unique=True, nullable=True)  # May not be assigned yet
    industry = Column(String(255))
    sector = Column(String(255))
    founded_year = Column(Integer)
    founder_name = Column(String(255))
    ceo_name = Column(String(255))
    headquarters = Column(String(255))
    website = Column(String(255))
    employee_count = Column(Integer)
    description = Column(Text)
    
    # Tracking fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    ipos = relationship("IPO", back_populates="company")
    reports = relationship("AIReport", back_populates="company")
    
    def __repr__(self):
        return f"<Company(name='{self.name}', ticker='{self.ticker_symbol}')>"

class IPO(Base):
    """
    Stores IPO-specific information and filing details
    """
    __tablename__ = 'ipos'
    
    id = Column(Integer, primary_key=True)
    company_id = Column(Integer, ForeignKey('companies.id'), nullable=False)
    
    # IPO Details
    filing_date = Column(DateTime)  # When S-1 was filed
    expected_date = Column(DateTime)  # Expected IPO date
    actual_date = Column(DateTime)  # Actual IPO date
    price_range_low = Column(Float)  # Expected price range
    price_range_high = Column(Float)
    final_price = Column(Float)  # Actual IPO price
    shares_offered = Column(Integer)
    market_cap_estimate = Column(Float)
    
    # Exchange information
    exchange = Column(String(10))  # NASDAQ, NYSE, etc.
    
    # SEC Filing information
    sec_filing_url = Column(String(500))
    cik_number = Column(String(20))  # SEC Central Index Key
    
    # Status tracking
    status = Column(String(50), default='filed')  # filed, priced, trading, withdrawn
    is_active = Column(Boolean, default=True)

    # User notes
    notes = Column(Text)  # Personal notes about this IPO

    # Tracking fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company = relationship("Company", back_populates="ipos")
    
    def __repr__(self):
        return f"<IPO(company_id={self.company_id}, status='{self.status}')>"

class AIReport(Base):
    """
    Stores AI-generated reports about companies
    """
    __tablename__ = 'ai_reports'
    
    id = Column(Integer, primary_key=True)
    company_id = Column(Integer, ForeignKey('companies.id'), nullable=False)
    
    # Report content
    business_model = Column(Text)
    competitive_analysis = Column(Text)
    market_opportunity = Column(Text)
    risk_factors = Column(Text)
    investment_thesis = Column(Text)
    summary = Column(Text)
    
    # Generation metadata
    ai_model_used = Column(String(100))  # e.g., "gpt-4", "gpt-3.5-turbo"
    generation_cost = Column(Float)  # Track API costs
    confidence_score = Column(Float)  # AI confidence in analysis
    
    # Tracking fields
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    company = relationship("Company", back_populates="reports")
    
    def __repr__(self):
        return f"<AIReport(company_id={self.company_id}, model='{self.ai_model_used}')>"

class DataSource(Base):
    """
    Tracks data sources and last update times for monitoring
    """
    __tablename__ = 'data_sources'
    
    id = Column(Integer, primary_key=True)
    source_name = Column(String(100), unique=True, nullable=False)  # e.g., "SEC_EDGAR", "ALPHA_VANTAGE"
    last_check = Column(DateTime)
    last_successful_update = Column(DateTime)
    total_requests_today = Column(Integer, default=0)
    rate_limit_remaining = Column(Integer)
    is_active = Column(Boolean, default=True)
    error_count = Column(Integer, default=0)
    last_error = Column(Text)
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"<DataSource(name='{self.source_name}', active={self.is_active})>"

# Database connection and session management
def create_database_engine():
    """Create database engine with connection pooling"""
    engine = create_engine(
        Config.DATABASE_URL,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,  # Verify connections before use
        echo=False  # Set to True for SQL debugging
    )
    return engine

def create_tables(engine):
    """Create all tables in the database"""
    Base.metadata.create_all(engine)

def get_session_maker(engine):
    """Get a session maker for database operations"""
    return sessionmaker(bind=engine)
