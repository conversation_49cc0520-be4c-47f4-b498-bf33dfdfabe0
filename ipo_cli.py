"""
IPO Tracker Command Line Interface
Provides easy access to all IPO tracking functionality.
"""

import click
import sys
from datetime import datetime, timedelta
from tabulate import tabulate
from loguru import logger
from database import get_db_session, db_manager
from models import Company, IPO, DataSource
from data_collectors import IPODataManager
from scheduler import IPOScheduler
from notes_manager import NotesManager
import threading
import time

@click.group()
def cli():
    """IPO Tracker - Automated IPO monitoring and analysis system"""
    pass

@cli.command()
def status():
    """Show system status and statistics"""
    click.echo("📊 IPO TRACKER STATUS")
    click.echo("=" * 50)
    
    try:
        # Database stats
        stats = db_manager.get_stats()
        click.echo(f"📈 Companies tracked: {stats.get('companies', 0)}")
        click.echo(f"📋 IPO filings: {stats.get('ipos', 0)}")
        click.echo(f"🤖 AI reports: {stats.get('ai_reports', 0)}")
        
        # Data source status
        with get_db_session() as session:
            sources = session.query(DataSource).all()
            
            if sources:
                click.echo("\n🔄 Data Sources:")
                source_data = []
                for source in sources:
                    last_check = source.last_check.strftime("%Y-%m-%d %H:%M") if source.last_check else "Never"
                    last_success = source.last_successful_update.strftime("%Y-%m-%d %H:%M") if source.last_successful_update else "Never"
                    status_icon = "✅" if source.error_count == 0 else "❌"
                    
                    source_data.append([
                        f"{status_icon} {source.source_name}",
                        last_check,
                        last_success,
                        source.error_count
                    ])
                
                click.echo(tabulate(source_data, headers=["Source", "Last Check", "Last Success", "Errors"]))
        
        # Recent IPOs
        with get_db_session() as session:
            recent_ipos = session.query(IPO).join(Company).order_by(IPO.created_at.desc()).limit(5).all()
            
            if recent_ipos:
                click.echo("\n📋 Recent IPOs:")
                ipo_data = []
                for ipo in recent_ipos:
                    filing_date = ipo.filing_date.strftime("%Y-%m-%d") if ipo.filing_date else "Unknown"
                    ipo_data.append([
                        ipo.company.name,
                        ipo.company.ticker_symbol or "TBD",
                        ipo.exchange or "Unknown",
                        filing_date,
                        ipo.status.title()
                    ])
                
                click.echo(tabulate(ipo_data, headers=["Company", "Ticker", "Exchange", "Filed", "Status"]))
    
    except Exception as e:
        click.echo(f"❌ Error getting status: {e}")

@cli.command()
@click.option('--days', default=7, help='Number of days to look back')
def collect(days):
    """Manually trigger IPO data collection"""
    click.echo(f"🔄 Collecting IPO data from last {days} days...")
    
    try:
        data_manager = IPODataManager()
        new_count = data_manager.collect_new_ipos()
        
        if new_count > 0:
            click.echo(f"✅ Successfully collected {new_count} new IPOs")
        else:
            click.echo("ℹ️  No new IPOs found")
            
    except Exception as e:
        click.echo(f"❌ Collection failed: {e}")
        sys.exit(1)

@cli.command()
@click.option('--background', '-b', is_flag=True, help='Run scheduler in background')
def schedule(background):
    """Start the background IPO monitoring scheduler"""
    if background:
        click.echo("🚀 Starting IPO scheduler in background...")
        # This would typically use a proper daemon process
        # For now, we'll run in a separate thread
        scheduler = IPOScheduler()
        scheduler_thread = threading.Thread(target=scheduler.start, daemon=True)
        scheduler_thread.start()
        
        click.echo("✅ Scheduler started in background")
        click.echo("Press Ctrl+C to stop")
        
        try:
            while scheduler.running:
                time.sleep(1)
        except KeyboardInterrupt:
            click.echo("\n🛑 Stopping scheduler...")
            scheduler.stop()
            scheduler_thread.join(timeout=5)
            click.echo("✅ Scheduler stopped")
    else:
        click.echo("🚀 Starting IPO scheduler...")
        try:
            scheduler = IPOScheduler()
            scheduler.start()
        except KeyboardInterrupt:
            click.echo("\n🛑 Scheduler stopped by user")

@cli.command()
@click.argument('query', required=False)
def search(query):
    """Search for companies and IPOs"""
    if not query:
        query = click.prompt("Enter search term (company name or ticker)")
    
    click.echo(f"🔍 Searching for: {query}")
    
    try:
        with get_db_session() as session:
            companies = session.query(Company).filter(
                (Company.name.ilike(f"%{query}%")) |
                (Company.ticker_symbol.ilike(f"%{query}%")) |
                (Company.industry.ilike(f"%{query}%"))
            ).limit(10).all()
            
            if not companies:
                click.echo("❌ No companies found")
                return
            
            click.echo(f"\n📋 Found {len(companies)} companies:")
            
            for company in companies:
                click.echo(f"\n🏢 {company.name}")
                if company.ticker_symbol:
                    click.echo(f"   Ticker: {company.ticker_symbol}")
                if company.industry:
                    click.echo(f"   Industry: {company.industry}")
                if company.ceo_name:
                    click.echo(f"   CEO: {company.ceo_name}")
                
                # Show IPO info
                ipo = session.query(IPO).filter(IPO.company_id == company.id).first()
                if ipo:
                    click.echo(f"   IPO Status: {ipo.status.title()}")
                    if ipo.filing_date:
                        click.echo(f"   Filed: {ipo.filing_date.strftime('%Y-%m-%d')}")
                    if ipo.exchange:
                        click.echo(f"   Exchange: {ipo.exchange}")
                
                click.echo("-" * 40)
    
    except Exception as e:
        click.echo(f"❌ Search failed: {e}")

@cli.command()
def notes():
    """Manage IPO notes"""
    click.echo("📝 IPO Notes Manager")
    
    try:
        from notes_manager import main as notes_main
        notes_main()
    except Exception as e:
        click.echo(f"❌ Failed to launch notes manager: {e}")

@cli.command()
@click.option('--limit', default=20, help='Number of IPOs to show')
@click.option('--status', help='Filter by status (filed, priced, trading, withdrawn)')
@click.option('--exchange', help='Filter by exchange (NASDAQ, NYSE)')
def list(limit, status, exchange):
    """List recent IPOs with optional filters"""
    click.echo("📋 IPO LISTINGS")
    click.echo("=" * 80)
    
    try:
        with get_db_session() as session:
            query = session.query(IPO).join(Company).order_by(IPO.created_at.desc())
            
            if status:
                query = query.filter(IPO.status == status.lower())
            
            if exchange:
                query = query.filter(IPO.exchange.ilike(f"%{exchange}%"))
            
            ipos = query.limit(limit).all()
            
            if not ipos:
                click.echo("❌ No IPOs found with the specified criteria")
                return
            
            # Prepare table data
            table_data = []
            for ipo in ipos:
                filing_date = ipo.filing_date.strftime("%Y-%m-%d") if ipo.filing_date else "Unknown"
                expected_date = ipo.expected_date.strftime("%Y-%m-%d") if ipo.expected_date else "TBD"
                price_range = ""
                if ipo.price_range_low and ipo.price_range_high:
                    price_range = f"${ipo.price_range_low}-${ipo.price_range_high}"
                
                has_notes = "📝" if ipo.notes else ""
                
                table_data.append([
                    ipo.company.name[:30],  # Truncate long names
                    ipo.company.ticker_symbol or "TBD",
                    ipo.exchange or "Unknown",
                    filing_date,
                    expected_date,
                    ipo.status.title(),
                    price_range,
                    has_notes
                ])
            
            headers = ["Company", "Ticker", "Exchange", "Filed", "Expected", "Status", "Price Range", "Notes"]
            click.echo(tabulate(table_data, headers=headers, tablefmt="grid"))
            
            click.echo(f"\nShowing {len(ipos)} of {limit} requested IPOs")
    
    except Exception as e:
        click.echo(f"❌ Failed to list IPOs: {e}")

@cli.command()
def init():
    """Initialize the IPO tracker database"""
    click.echo("🔄 Initializing IPO Tracker database...")
    
    try:
        from database import init_database
        init_database()
        click.echo("✅ Database initialized successfully!")
    except Exception as e:
        click.echo(f"❌ Database initialization failed: {e}")
        sys.exit(1)

@cli.command()
def test():
    """Run system tests"""
    click.echo("🧪 Running IPO Tracker tests...")
    
    try:
        from test_setup import main as test_main
        success = test_main()
        if success:
            click.echo("✅ All tests passed!")
        else:
            click.echo("❌ Some tests failed")
            sys.exit(1)
    except Exception as e:
        click.echo(f"❌ Test execution failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    cli()
