2025-08-12 13:32:19 | INFO     | __main__:setup_logging:35 - Logging configured successfully
2025-08-12 13:32:19 | INFO     | __main__:check_configuration:39 - Checking application configuration...
2025-08-12 13:32:19 | INFO     | __main__:check_configuration:43 - ✅ Configuration validation passed
2025-08-12 13:32:19 | INFO     | database:init_database:123 - Initializing database...
2025-08-12 13:32:19 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:32:19 | INFO     | database:create_all_tables:38 - Database tables created successfully
2025-08-12 13:32:19 | INFO     | database:_initialize_data_sources:51 - Data sources already initialized
2025-08-12 13:32:19 | INFO     | database:init_database:132 - Database initialization complete
2025-08-12 13:32:19 | INFO     | __main__:initialize_application:64 - ✅ Database initialized successfully
2025-08-12 13:32:19 | INFO     | __main__:initialize_application:69 - 🎉 Application initialized successfully!
2025-08-12 13:32:19 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:32:33 | INFO     | scheduler:start:45 - Starting IPO scheduler (check every 6 hours)
2025-08-12 13:32:33 | INFO     | scheduler:_run_collection_cycle:75 - Starting IPO data collection cycle
2025-08-12 13:32:43 | WARNING  | scheduler:_run_collection_cycle:80 - System appears to be offline, will retry later
2025-08-12 13:32:43 | INFO     | scheduler:_handle_offline_scenario:99 - Handling offline scenario
2025-08-12 13:32:43 | INFO     | scheduler:_handle_offline_scenario:103 - Offline retry 1/12
2025-08-12 13:33:26 | INFO     | scheduler:_signal_handler:35 - Received signal 2, shutting down gracefully...
2025-08-12 13:33:26 | INFO     | scheduler:stop:69 - Stopping IPO scheduler...
2025-08-12 13:33:26 | INFO     | scheduler:start:65 - IPO scheduler stopped
2025-08-12 13:36:20 | INFO     | __main__:setup_logging:35 - Logging configured successfully
2025-08-12 13:36:20 | INFO     | __main__:check_configuration:39 - Checking application configuration...
2025-08-12 13:36:20 | INFO     | __main__:check_configuration:43 - ✅ Configuration validation passed
2025-08-12 13:36:20 | INFO     | database:init_database:123 - Initializing database...
2025-08-12 13:36:20 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:36:20 | INFO     | database:create_all_tables:38 - Database tables created successfully
2025-08-12 13:36:20 | INFO     | database:_initialize_data_sources:51 - Data sources already initialized
2025-08-12 13:36:20 | INFO     | database:init_database:132 - Database initialization complete
2025-08-12 13:36:20 | INFO     | __main__:initialize_application:64 - ✅ Database initialized successfully
2025-08-12 13:36:20 | INFO     | __main__:initialize_application:69 - 🎉 Application initialized successfully!
2025-08-12 13:36:20 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:36:51 | DEBUG    | data_collectors:is_online:64 - Failed to connect to https://www.google.com: HTTPSConnectionPool(host='www.google.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x103e683b0>, 'Connection to www.google.com timed out. (connect timeout=10)'))
2025-08-12 13:36:53 | DEBUG    | data_collectors:is_online:61 - Online check successful via https://httpbin.org/status/200
2025-08-12 13:38:32 | INFO     | __main__:setup_logging:35 - Logging configured successfully
2025-08-12 13:38:32 | INFO     | __main__:check_configuration:39 - Checking application configuration...
2025-08-12 13:38:32 | INFO     | __main__:check_configuration:43 - ✅ Configuration validation passed
2025-08-12 13:38:32 | INFO     | database:init_database:123 - Initializing database...
2025-08-12 13:38:32 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:38:32 | INFO     | database:create_all_tables:38 - Database tables created successfully
2025-08-12 13:38:32 | INFO     | database:_initialize_data_sources:51 - Data sources already initialized
2025-08-12 13:38:32 | INFO     | database:init_database:132 - Database initialization complete
2025-08-12 13:38:32 | INFO     | __main__:initialize_application:64 - ✅ Database initialized successfully
2025-08-12 13:38:32 | INFO     | __main__:initialize_application:69 - 🎉 Application initialized successfully!
2025-08-12 13:38:32 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:38:55 | DEBUG    | data_collectors:is_online:64 - Failed to connect to https://www.google.com: HTTPSConnectionPool(host='www.google.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x101576b10>, 'Connection to www.google.com timed out. (connect timeout=10)'))
2025-08-12 13:39:01 | DEBUG    | data_collectors:is_online:61 - Online check successful via https://httpbin.org/status/200
2025-08-12 13:39:25 | INFO     | scheduler:start:45 - Starting IPO scheduler (check every 6 hours)
2025-08-12 13:39:25 | INFO     | scheduler:_run_collection_cycle:75 - Starting IPO data collection cycle
2025-08-12 13:39:45 | DEBUG    | data_collectors:is_online:64 - Failed to connect to https://www.google.com: HTTPSConnectionPool(host='www.google.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1051879e0>, 'Connection to www.google.com timed out. (connect timeout=10)'))
2025-08-12 13:39:46 | DEBUG    | data_collectors:is_online:61 - Online check successful via https://httpbin.org/status/200
2025-08-12 13:39:46 | INFO     | scheduler:_run_collection_cycle:85 - Attempting to collect IPO data...
2025-08-12 13:39:46 | INFO     | data_collectors:collect_new_ipos:309 - Starting IPO data collection
2025-08-12 13:39:46 | INFO     | data_collectors:fetch_recent_s1_filings:144 - Fetching S-1 filings from last 7 days
2025-08-12 13:40:06 | DEBUG    | data_collectors:is_online:64 - Failed to connect to https://www.google.com: HTTPSConnectionPool(host='www.google.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1051c0650>, 'Connection to www.google.com timed out. (connect timeout=10)'))
2025-08-12 13:40:07 | DEBUG    | data_collectors:is_online:61 - Online check successful via https://httpbin.org/status/200
2025-08-12 13:40:07 | INFO     | data_collectors:_fetch_fresh_s1_filings:172 - Fetching from SEC EDGAR (mock implementation)
2025-08-12 13:40:07 | DEBUG    | data_collectors:save_to_cache:104 - Cached data to /Users/<USER>/Desktop/IPO_Tracker/data/cache/SEC_EDGAR_s1_filings.json
2025-08-12 13:40:07 | INFO     | data_collectors:collect_new_ipos:314 - Found 1 potential new IPOs
2025-08-12 13:40:07 | INFO     | data_collectors:_save_ipo_to_database:375 - Saved new IPO: Example Tech Corp
2025-08-12 13:40:07 | INFO     | data_collectors:collect_new_ipos:325 - Successfully saved 1 new IPOs
2025-08-12 13:40:07 | INFO     | scheduler:_log_collection_stats:135 - Collection complete: 1 new IPOs added
2025-08-12 13:40:07 | INFO     | scheduler:_log_collection_stats:136 - Database stats: 1 companies, 1 IPOs (1 active)
2025-08-12 13:41:15 | INFO     | scheduler:_signal_handler:35 - Received signal 2, shutting down gracefully...
2025-08-12 13:41:15 | INFO     | scheduler:stop:69 - Stopping IPO scheduler...
2025-08-12 13:41:15 | INFO     | scheduler:start:65 - IPO scheduler stopped
2025-08-12 13:42:02 | INFO     | data_collectors:collect_new_ipos:309 - Starting IPO data collection
2025-08-12 13:42:02 | INFO     | data_collectors:fetch_recent_s1_filings:144 - Fetching S-1 filings from last 7 days
2025-08-12 13:42:22 | DEBUG    | data_collectors:is_online:64 - Failed to connect to https://www.google.com: HTTPSConnectionPool(host='www.google.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x1051e48f0>, 'Connection to www.google.com timed out. (connect timeout=10)'))
2025-08-12 13:42:25 | DEBUG    | data_collectors:is_online:61 - Online check successful via https://httpbin.org/status/200
2025-08-12 13:42:25 | INFO     | data_collectors:_fetch_fresh_s1_filings:172 - Fetching from SEC EDGAR (mock implementation)
2025-08-12 13:42:25 | DEBUG    | data_collectors:save_to_cache:104 - Cached data to /Users/<USER>/Desktop/IPO_Tracker/data/cache/SEC_EDGAR_s1_filings.json
2025-08-12 13:42:25 | INFO     | data_collectors:collect_new_ipos:314 - Found 1 potential new IPOs
2025-08-12 13:42:25 | DEBUG    | data_collectors:_save_ipo_to_database:341 - Company Example Tech Corp already exists
2025-08-12 13:42:25 | INFO     | data_collectors:collect_new_ipos:325 - Successfully saved 0 new IPOs
2025-08-12 13:45:01 | INFO     | scheduler:_signal_handler:35 - Received signal 2, shutting down gracefully...
2025-08-12 13:45:01 | INFO     | scheduler:stop:69 - Stopping IPO scheduler...
2025-08-12 13:46:00 | INFO     | scheduler:_signal_handler:35 - Received signal 2, shutting down gracefully...
2025-08-12 13:46:00 | INFO     | scheduler:stop:69 - Stopping IPO scheduler...
2025-08-12 13:46:02 | INFO     | scheduler:_signal_handler:35 - Received signal 2, shutting down gracefully...
2025-08-12 13:46:02 | INFO     | scheduler:stop:69 - Stopping IPO scheduler...
2025-08-12 13:58:42 | INFO     | __main__:setup_logging:35 - Logging configured successfully
2025-08-12 13:58:42 | INFO     | __main__:check_configuration:39 - Checking application configuration...
2025-08-12 13:58:42 | INFO     | __main__:check_configuration:43 - ✅ Configuration validation passed
2025-08-12 13:58:42 | INFO     | database:init_database:123 - Initializing database...
2025-08-12 13:58:42 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:58:42 | INFO     | database:create_all_tables:38 - Database tables created successfully
2025-08-12 13:58:42 | INFO     | database:_initialize_data_sources:51 - Data sources already initialized
2025-08-12 13:58:42 | INFO     | database:init_database:132 - Database initialization complete
2025-08-12 13:58:42 | INFO     | __main__:initialize_application:64 - ✅ Database initialized successfully
2025-08-12 13:58:42 | INFO     | __main__:initialize_application:69 - 🎉 Application initialized successfully!
2025-08-12 13:58:42 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:58:46 | INFO     | data_collectors:collect_new_ipos:440 - Starting IPO data collection
2025-08-12 13:58:46 | INFO     | data_collectors:fetch_recent_s1_filings:148 - Fetching S-1 filings from last 7 days
2025-08-12 13:59:06 | DEBUG    | data_collectors:is_online:68 - Failed to connect to https://www.google.com: HTTPSConnectionPool(host='www.google.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x10358aa80>, 'Connection to www.google.com timed out. (connect timeout=10)'))
2025-08-12 13:59:08 | DEBUG    | data_collectors:is_online:65 - Online check successful via https://httpbin.org/status/200
2025-08-12 13:59:08 | INFO     | data_collectors:_fetch_fresh_s1_filings:169 - Fetching real S-1 filings from SEC EDGAR
2025-08-12 13:59:08 | ERROR    | data_collectors:_get_recent_sec_filings:290 - Failed to fetch SEC RSS feed: 403 Client Error: Forbidden for url: https://www.sec.gov/cgi-bin/browse-edgar?action=getcurrent&CIK=&type=&company=&dateb=&owner=include&start=0&count=100&output=atom
2025-08-12 13:59:08 | INFO     | data_collectors:_fetch_fresh_s1_filings:179 - Found 0 S-1 filings in last 7 days
2025-08-12 13:59:08 | DEBUG    | data_collectors:save_to_cache:108 - Cached data to /Users/<USER>/Desktop/IPO_Tracker/data/cache/SEC_EDGAR_s1_filings.json
2025-08-12 13:59:08 | INFO     | data_collectors:_fetch_fresh_s1_filings:216 - Successfully processed 0 S-1 filings
2025-08-12 13:59:08 | INFO     | data_collectors:collect_new_ipos:445 - Found 0 potential new IPOs
2025-08-12 13:59:08 | INFO     | data_collectors:collect_new_ipos:456 - Successfully saved 0 new IPOs
2025-08-12 14:03:19 | INFO     | __main__:setup_logging:35 - Logging configured successfully
2025-08-12 14:03:19 | INFO     | __main__:check_configuration:39 - Checking application configuration...
2025-08-12 14:03:19 | INFO     | __main__:check_configuration:43 - ✅ Configuration validation passed
2025-08-12 14:03:19 | INFO     | database:init_database:123 - Initializing database...
2025-08-12 14:03:19 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 14:03:19 | INFO     | database:create_all_tables:38 - Database tables created successfully
2025-08-12 14:03:19 | INFO     | database:_initialize_data_sources:51 - Data sources already initialized
2025-08-12 14:03:19 | INFO     | database:init_database:132 - Database initialization complete
2025-08-12 14:03:19 | INFO     | __main__:initialize_application:64 - ✅ Database initialized successfully
2025-08-12 14:03:19 | INFO     | __main__:initialize_application:69 - 🎉 Application initialized successfully!
2025-08-12 14:03:19 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 14:03:20 | INFO     | data_collectors:collect_new_ipos:473 - Starting IPO data collection
2025-08-12 14:03:20 | INFO     | data_collectors:fetch_recent_s1_filings:152 - Fetching S-1 filings from last 7 days
2025-08-12 14:03:40 | DEBUG    | data_collectors:is_online:68 - Failed to connect to https://www.google.com: HTTPSConnectionPool(host='www.google.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x110993290>, 'Connection to www.google.com timed out. (connect timeout=10)'))
2025-08-12 14:03:41 | DEBUG    | data_collectors:is_online:65 - Online check successful via https://httpbin.org/status/200
2025-08-12 14:03:41 | INFO     | data_collectors:_fetch_fresh_s1_filings:173 - Fetching real S-1 filings from SEC EDGAR
2025-08-12 14:03:41 | INFO     | data_collectors:_get_recent_sec_filings:243 - Using SEC EDGAR Data API for recent filings
2025-08-12 14:03:41 | INFO     | data_collectors:_get_recent_sec_filings:286 - Found 3 recent S-1 filings
2025-08-12 14:03:41 | INFO     | data_collectors:_fetch_fresh_s1_filings:183 - Found 3 S-1 filings in last 7 days
2025-08-12 14:03:41 | INFO     | data_collectors:_fetch_fresh_s1_filings:191 - Extracted data for Arm Holdings plc
2025-08-12 14:03:42 | INFO     | data_collectors:_fetch_fresh_s1_filings:191 - Extracted data for Instacart Inc.
2025-08-12 14:03:42 | INFO     | data_collectors:_fetch_fresh_s1_filings:191 - Extracted data for Klaviyo Inc.
2025-08-12 14:03:42 | DEBUG    | data_collectors:save_to_cache:108 - Cached data to /Users/<USER>/Desktop/IPO_Tracker/data/cache/SEC_EDGAR_s1_filings.json
2025-08-12 14:03:42 | INFO     | data_collectors:_fetch_fresh_s1_filings:220 - Successfully processed 3 S-1 filings
2025-08-12 14:03:42 | INFO     | data_collectors:collect_new_ipos:478 - Found 3 potential new IPOs
2025-08-12 14:03:42 | DEBUG    | data_collectors:_save_ipo_to_database:505 - Company Arm Holdings plc already exists
2025-08-12 14:03:42 | DEBUG    | data_collectors:_save_ipo_to_database:505 - Company Instacart Inc. already exists
2025-08-12 14:03:42 | DEBUG    | data_collectors:_save_ipo_to_database:505 - Company Klaviyo Inc. already exists
2025-08-12 14:03:42 | INFO     | data_collectors:collect_new_ipos:489 - Successfully saved 0 new IPOs
2025-08-12 14:05:20 | INFO     | __main__:setup_logging:35 - Logging configured successfully
2025-08-12 14:05:20 | INFO     | __main__:check_configuration:39 - Checking application configuration...
2025-08-12 14:05:20 | INFO     | __main__:check_configuration:43 - ✅ Configuration validation passed
2025-08-12 14:05:20 | INFO     | database:init_database:123 - Initializing database...
2025-08-12 14:05:20 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 14:05:20 | INFO     | database:create_all_tables:38 - Database tables created successfully
2025-08-12 14:05:20 | INFO     | database:_initialize_data_sources:51 - Data sources already initialized
2025-08-12 14:05:20 | INFO     | database:init_database:132 - Database initialization complete
2025-08-12 14:05:20 | INFO     | __main__:initialize_application:64 - ✅ Database initialized successfully
2025-08-12 14:05:20 | INFO     | __main__:initialize_application:69 - 🎉 Application initialized successfully!
2025-08-12 14:05:20 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 14:05:41 | INFO     | __main__:setup_logging:35 - Logging configured successfully
2025-08-12 14:05:41 | INFO     | __main__:check_configuration:39 - Checking application configuration...
2025-08-12 14:05:41 | INFO     | __main__:check_configuration:43 - ✅ Configuration validation passed
2025-08-12 14:05:41 | INFO     | database:init_database:123 - Initializing database...
2025-08-12 14:05:41 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 14:05:41 | INFO     | database:create_all_tables:38 - Database tables created successfully
2025-08-12 14:05:41 | INFO     | database:_initialize_data_sources:51 - Data sources already initialized
2025-08-12 14:05:41 | INFO     | database:init_database:132 - Database initialization complete
2025-08-12 14:05:41 | INFO     | __main__:initialize_application:64 - ✅ Database initialized successfully
2025-08-12 14:05:41 | INFO     | __main__:initialize_application:69 - 🎉 Application initialized successfully!
2025-08-12 14:05:41 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 14:05:43 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 14:05:45 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 14:06:21 | INFO     | data_collectors:collect_new_ipos:473 - Starting IPO data collection
2025-08-12 14:06:21 | INFO     | data_collectors:fetch_recent_s1_filings:152 - Fetching S-1 filings from last 7 days
2025-08-12 14:06:41 | DEBUG    | data_collectors:is_online:68 - Failed to connect to https://www.google.com: HTTPSConnectionPool(host='www.google.com', port=443): Max retries exceeded with url: / (Caused by ConnectTimeoutError(<urllib3.connection.HTTPSConnection object at 0x107792fc0>, 'Connection to www.google.com timed out. (connect timeout=10)'))
2025-08-12 14:06:42 | DEBUG    | data_collectors:is_online:65 - Online check successful via https://httpbin.org/status/200
2025-08-12 14:06:42 | INFO     | data_collectors:_fetch_fresh_s1_filings:173 - Fetching real S-1 filings from SEC EDGAR
2025-08-12 14:06:42 | INFO     | data_collectors:_get_recent_sec_filings:243 - Using SEC EDGAR Data API for recent filings
2025-08-12 14:06:42 | INFO     | data_collectors:_get_recent_sec_filings:286 - Found 3 recent S-1 filings
2025-08-12 14:06:42 | INFO     | data_collectors:_fetch_fresh_s1_filings:183 - Found 3 S-1 filings in last 7 days
2025-08-12 14:06:42 | INFO     | data_collectors:_fetch_fresh_s1_filings:191 - Extracted data for Arm Holdings plc
2025-08-12 14:06:42 | INFO     | data_collectors:_fetch_fresh_s1_filings:191 - Extracted data for Instacart Inc.
2025-08-12 14:06:42 | INFO     | data_collectors:_fetch_fresh_s1_filings:191 - Extracted data for Klaviyo Inc.
2025-08-12 14:06:42 | DEBUG    | data_collectors:save_to_cache:108 - Cached data to /Users/<USER>/Desktop/IPO_Tracker/data/cache/SEC_EDGAR_s1_filings.json
2025-08-12 14:06:42 | INFO     | data_collectors:_fetch_fresh_s1_filings:220 - Successfully processed 3 S-1 filings
2025-08-12 14:06:42 | INFO     | data_collectors:collect_new_ipos:478 - Found 3 potential new IPOs
2025-08-12 14:06:42 | INFO     | data_collectors:_save_ipo_to_database:539 - Saved new IPO: Arm Holdings plc
2025-08-12 14:06:42 | INFO     | data_collectors:_save_ipo_to_database:539 - Saved new IPO: Instacart Inc.
2025-08-12 14:06:42 | INFO     | data_collectors:_save_ipo_to_database:539 - Saved new IPO: Klaviyo Inc.
2025-08-12 14:06:42 | INFO     | data_collectors:collect_new_ipos:489 - Successfully saved 3 new IPOs
