2025-08-12 13:32:19 | INFO     | __main__:setup_logging:35 - Logging configured successfully
2025-08-12 13:32:19 | INFO     | __main__:check_configuration:39 - Checking application configuration...
2025-08-12 13:32:19 | INFO     | __main__:check_configuration:43 - ✅ Configuration validation passed
2025-08-12 13:32:19 | INFO     | database:init_database:123 - Initializing database...
2025-08-12 13:32:19 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:32:19 | INFO     | database:create_all_tables:38 - Database tables created successfully
2025-08-12 13:32:19 | INFO     | database:_initialize_data_sources:51 - Data sources already initialized
2025-08-12 13:32:19 | INFO     | database:init_database:132 - Database initialization complete
2025-08-12 13:32:19 | INFO     | __main__:initialize_application:64 - ✅ Database initialized successfully
2025-08-12 13:32:19 | INFO     | __main__:initialize_application:69 - 🎉 Application initialized successfully!
2025-08-12 13:32:19 | INFO     | database:test_connection:95 - Database connection test successful
2025-08-12 13:32:33 | INFO     | scheduler:start:45 - Starting IPO scheduler (check every 6 hours)
2025-08-12 13:32:33 | INFO     | scheduler:_run_collection_cycle:75 - Starting IPO data collection cycle
2025-08-12 13:32:43 | WARNING  | scheduler:_run_collection_cycle:80 - System appears to be offline, will retry later
2025-08-12 13:32:43 | INFO     | scheduler:_handle_offline_scenario:99 - Handling offline scenario
2025-08-12 13:32:43 | INFO     | scheduler:_handle_offline_scenario:103 - Offline retry 1/12
2025-08-12 13:33:26 | INFO     | scheduler:_signal_handler:35 - Received signal 2, shutting down gracefully...
2025-08-12 13:33:26 | INFO     | scheduler:stop:69 - Stopping IPO scheduler...
2025-08-12 13:33:26 | INFO     | scheduler:start:65 - IPO scheduler stopped
