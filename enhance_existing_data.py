"""
Script to enhance existing IPO data with better information extraction
"""

import sys
from database import get_db_session
from models import Company, IPO
from data_collectors import SECEdgarCollector

def enhance_existing_companies():
    """Enhance existing companies with better data extraction"""
    print("🔄 Enhancing existing company data...")
    
    try:
        collector = SECEdgarCollector()
        
        with get_db_session() as session:
            # Get companies with missing information
            companies = session.query(Company).all()
            
            print(f"Found {len(companies)} companies to potentially enhance")
            
            enhanced_count = 0
            for company in companies:
                try:
                    print(f"\n🏢 Processing: {company.name}")
                    
                    # Get IPO info for CIK
                    ipo = session.query(IPO).filter(IPO.company_id == company.id).first()
                    cik = ipo.cik_number if ipo else None
                    
                    # Get enhanced information
                    enhanced_info = collector._get_detailed_company_info(cik, company.name)
                    
                    # Update missing fields
                    updated = False
                    
                    if not company.industry and enhanced_info.get('industry'):
                        company.industry = enhanced_info['industry']
                        print(f"   ✅ Added industry: {company.industry}")
                        updated = True
                    
                    if not company.sector and enhanced_info.get('sector'):
                        company.sector = enhanced_info['sector']
                        print(f"   ✅ Added sector: {company.sector}")
                        updated = True
                    
                    if not company.headquarters and enhanced_info.get('headquarters'):
                        company.headquarters = enhanced_info['headquarters']
                        print(f"   ✅ Added headquarters: {company.headquarters}")
                        updated = True
                    
                    if not company.description and enhanced_info.get('description'):
                        company.description = enhanced_info['description']
                        print(f"   ✅ Added description: {company.description[:100]}...")
                        updated = True
                    
                    # Update IPO exchange if missing
                    if ipo and not ipo.exchange and enhanced_info.get('exchange'):
                        ipo.exchange = enhanced_info['exchange']
                        print(f"   ✅ Added exchange: {ipo.exchange}")
                        updated = True
                    
                    if updated:
                        enhanced_count += 1
                        session.commit()
                        print(f"   ✅ Enhanced {company.name}")
                    else:
                        print(f"   ℹ️  No enhancements needed for {company.name}")
                    
                except Exception as e:
                    print(f"   ❌ Failed to enhance {company.name}: {e}")
                    continue
            
            print(f"\n🎉 Enhanced {enhanced_count} companies")
            return enhanced_count > 0
            
    except Exception as e:
        print(f"❌ Enhancement failed: {e}")
        return False

def show_enhanced_data():
    """Show the enhanced data"""
    print("\n📊 Enhanced Company Data:")
    print("=" * 80)
    
    try:
        with get_db_session() as session:
            companies = session.query(Company).all()
            
            for company in companies:
                print(f"\n🏢 {company.name}")
                print(f"   Industry: {company.industry or 'Not specified'}")
                print(f"   Sector: {company.sector or 'Not specified'}")
                print(f"   Headquarters: {company.headquarters or 'Not specified'}")
                
                if company.description:
                    desc = company.description[:150] + "..." if len(company.description) > 150 else company.description
                    print(f"   Description: {desc}")
                
                # Show IPO info
                ipo = session.query(IPO).filter(IPO.company_id == company.id).first()
                if ipo:
                    print(f"   Exchange: {ipo.exchange or 'Not specified'}")
                    print(f"   Filed: {ipo.filing_date.strftime('%Y-%m-%d') if ipo.filing_date else 'Unknown'}")
                    print(f"   CIK: {ipo.cik_number or 'Unknown'}")
                
                print("-" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to show data: {e}")
        return False

def analyze_data_completeness():
    """Analyze how complete our data is"""
    print("\n📈 Data Completeness Analysis:")
    print("=" * 50)
    
    try:
        with get_db_session() as session:
            companies = session.query(Company).all()
            total = len(companies)
            
            if total == 0:
                print("No companies found in database")
                return True
            
            # Count fields
            fields = {
                'Industry': sum(1 for c in companies if c.industry),
                'Sector': sum(1 for c in companies if c.sector),
                'Headquarters': sum(1 for c in companies if c.headquarters),
                'Description': sum(1 for c in companies if c.description),
                'CEO': sum(1 for c in companies if c.ceo_name),
                'Ticker': sum(1 for c in companies if c.ticker_symbol)
            }
            
            # IPO-specific fields
            ipos = session.query(IPO).all()
            ipo_fields = {
                'Exchange': sum(1 for i in ipos if i.exchange),
                'Filing Date': sum(1 for i in ipos if i.filing_date),
                'CIK Number': sum(1 for i in ipos if i.cik_number),
                'SEC URL': sum(1 for i in ipos if i.sec_filing_url)
            }
            
            print(f"Total Companies: {total}")
            print(f"Total IPOs: {len(ipos)}")
            print()
            
            print("Company Field Completeness:")
            for field, count in fields.items():
                percentage = (count / total) * 100
                print(f"   {field}: {count}/{total} ({percentage:.1f}%)")
            
            print("\nIPO Field Completeness:")
            for field, count in ipo_fields.items():
                percentage = (count / len(ipos)) * 100 if ipos else 0
                print(f"   {field}: {count}/{len(ipos)} ({percentage:.1f}%)")
            
            return True
            
    except Exception as e:
        print(f"❌ Analysis failed: {e}")
        return False

def main():
    """Main enhancement process"""
    print("🚀 IPO DATA ENHANCEMENT")
    print("=" * 50)
    
    steps = [
        ("Analyze Current Data", analyze_data_completeness),
        ("Enhance Companies", enhance_existing_companies),
        ("Show Enhanced Data", show_enhanced_data),
        ("Final Analysis", analyze_data_completeness),
    ]
    
    for step_name, step_func in steps:
        print(f"\n{'='*15} {step_name} {'='*15}")
        try:
            step_func()
        except Exception as e:
            print(f"❌ {step_name} failed: {e}")
    
    print(f"\n{'='*50}")
    print("🎉 Data enhancement complete!")
    print("\nYour IPO data now has:")
    print("✅ Better industry classifications")
    print("✅ Sector assignments")
    print("✅ Exchange predictions")
    print("✅ Company descriptions")
    print("✅ Headquarters information (when available)")
    
    print("\nNext time you collect IPO data, it will automatically")
    print("include this enhanced information extraction!")

if __name__ == "__main__":
    main()
