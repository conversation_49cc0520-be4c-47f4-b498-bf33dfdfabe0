"""
Test script for real SEC EDGAR data integration
"""

import sys
from datetime import datetime
from database import get_db_session, init_database
from models import Company, IPO
from data_collectors import IPODataManager, SECEdgarCollector

def test_sec_connectivity():
    """Test connectivity to SEC EDGAR"""
    print("🔄 Testing SEC EDGAR connectivity...")
    
    try:
        collector = SECEdgarCollector()
        
        # Test basic connectivity
        if not collector.is_online():
            print("❌ No internet connection detected")
            return False
        
        print("✅ Internet connection working")
        
        # Test SEC-specific connectivity
        import requests
        response = requests.get("https://www.sec.gov", timeout=10)
        if response.status_code == 200:
            print("✅ SEC.gov is accessible")
        else:
            print(f"⚠️  SEC.gov returned status {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ SEC connectivity test failed: {e}")
        return False

def test_sec_rss_feed():
    """Test SEC RSS feed parsing"""
    print("\n🔄 Testing SEC RSS feed parsing...")
    
    try:
        collector = SECEdgarCollector()
        
        # Get recent filings
        print("   Fetching recent filings...")
        recent_filings = collector._get_recent_sec_filings(days_back=3)
        
        print(f"✅ Found {len(recent_filings)} recent filings")
        
        if recent_filings:
            print("\n📋 Sample filings:")
            for i, filing in enumerate(recent_filings[:5]):  # Show first 5
                print(f"   {i+1}. {filing.get('form_type', 'Unknown')} - {filing.get('company_name', 'Unknown Company')}")
                if filing.get('filing_date'):
                    print(f"      Filed: {filing['filing_date'].strftime('%Y-%m-%d %H:%M')}")
                if filing.get('cik_number'):
                    print(f"      CIK: {filing['cik_number']}")
        
        # Look for S-1 filings specifically
        s1_filings = [f for f in recent_filings if f.get('form_type') == 'S-1']
        print(f"\n📊 S-1 filings (IPOs): {len(s1_filings)}")
        
        if s1_filings:
            print("🎉 Found S-1 IPO filings:")
            for filing in s1_filings:
                print(f"   • {filing.get('company_name', 'Unknown')} (CIK: {filing.get('cik_number', 'Unknown')})")
        else:
            print("ℹ️  No S-1 filings found in the last 3 days (this is normal)")
        
        return True
        
    except Exception as e:
        print(f"❌ RSS feed test failed: {e}")
        return False

def test_full_ipo_collection():
    """Test the full IPO collection process with real data"""
    print("\n🔄 Testing full IPO collection with real SEC data...")
    
    try:
        # Initialize fresh database
        print("   Initializing database...")
        init_database()
        
        # Test data collection
        print("   Starting IPO data collection...")
        data_manager = IPODataManager()
        new_count = data_manager.collect_new_ipos()
        
        print(f"✅ Collection completed: {new_count} new IPOs found")
        
        # Show what we collected
        with get_db_session() as session:
            companies = session.query(Company).all()
            ipos = session.query(IPO).all()
            
            print(f"\n📊 Database contents:")
            print(f"   Companies: {len(companies)}")
            print(f"   IPOs: {len(ipos)}")
            
            if companies:
                print(f"\n📋 Companies found:")
                for company in companies:
                    print(f"   🏢 {company.name}")
                    if company.ticker_symbol:
                        print(f"      Ticker: {company.ticker_symbol}")
                    if company.industry:
                        print(f"      Industry: {company.industry}")
                    
                    # Show IPO info
                    ipo = session.query(IPO).filter(IPO.company_id == company.id).first()
                    if ipo:
                        print(f"      IPO Status: {ipo.status}")
                        if ipo.filing_date:
                            print(f"      Filed: {ipo.filing_date.strftime('%Y-%m-%d')}")
                        if ipo.exchange:
                            print(f"      Exchange: {ipo.exchange}")
                        if ipo.sec_filing_url:
                            print(f"      SEC URL: {ipo.sec_filing_url}")
                    print()
            else:
                print("ℹ️  No companies found (this is normal if no recent S-1 filings)")
        
        return True
        
    except Exception as e:
        print(f"❌ Full collection test failed: {e}")
        return False

def test_data_caching():
    """Test offline data caching functionality"""
    print("\n🔄 Testing data caching...")
    
    try:
        collector = SECEdgarCollector()
        
        # Check if we have cached data
        cached_data = collector.load_from_cache("s1_filings", max_age_hours=24)
        
        if cached_data:
            print(f"✅ Found cached data: {len(cached_data)} entries")
            print("   This data will be used when offline")
        else:
            print("ℹ️  No cached data found (normal for first run)")
        
        return True
        
    except Exception as e:
        print(f"❌ Caching test failed: {e}")
        return False

def main():
    """Run all SEC integration tests"""
    print("🚀 SEC EDGAR INTEGRATION TEST")
    print("=" * 60)
    
    tests = [
        ("SEC Connectivity", test_sec_connectivity),
        ("RSS Feed Parsing", test_sec_rss_feed),
        ("Full IPO Collection", test_full_ipo_collection),
        ("Data Caching", test_data_caching),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*20} {test_name} {'='*20}")
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: CRASHED - {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 All tests passed! Real SEC EDGAR integration is working!")
        print("\nYour system now:")
        print("✅ Fetches real IPO data from SEC EDGAR")
        print("✅ Parses actual S-1 filing information")
        print("✅ Handles offline scenarios with caching")
        print("✅ Processes live NASDAQ/NYSE IPO filings")
        
        print("\nNext steps:")
        print("1. Run 'python main.py' and try option 6 (Collect IPO Data)")
        print("2. Start the background scheduler (option 7) for automatic monitoring")
        print("3. The system will now track real IPO filings automatically!")
    else:
        print(f"\n⚠️  {len(tests) - passed} test(s) failed")
        print("Check the errors above and ensure:")
        print("- Internet connection is working")
        print("- SEC.gov is accessible")
        print("- Database is properly configured")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
