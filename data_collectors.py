"""
IPO Data Collection System
Handles fetching IPO data from various sources with offline resilience.
"""

import requests
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from loguru import logger
from database import get_db_session
from models import Company, IPO, DataSource
from config import Config
import json
import os
import xml.etree.ElementTree as ET
from urllib.parse import urljoin
import re
from bs4 import BeautifulSoup

@dataclass
class IPOData:
    """Structure for IPO data"""
    company_name: str
    ticker_symbol: Optional[str] = None
    industry: Optional[str] = None
    sector: Optional[str] = None
    ceo_name: Optional[str] = None
    founded_year: Optional[int] = None
    headquarters: Optional[str] = None
    website: Optional[str] = None
    description: Optional[str] = None
    filing_date: Optional[datetime] = None
    expected_date: Optional[datetime] = None
    price_range_low: Optional[float] = None
    price_range_high: Optional[float] = None
    shares_offered: Optional[int] = None
    exchange: Optional[str] = None
    sec_filing_url: Optional[str] = None
    cik_number: Optional[str] = None

class DataCollectorBase:
    """Base class for all data collectors with offline handling"""
    
    def __init__(self, source_name: str):
        self.source_name = source_name
        self.cache_dir = Config.DATA_DIR / "cache"
        self.cache_dir.mkdir(exist_ok=True)
        self.last_successful_fetch = None
        
    def is_online(self) -> bool:
        """Check if we have internet connectivity"""
        test_urls = [
            "https://www.google.com",
            "https://httpbin.org/status/200",
            "https://www.sec.gov",
            "https://www.alphavantage.co"
        ]

        for url in test_urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    logger.debug(f"Online check successful via {url}")
                    return True
            except Exception as e:
                logger.debug(f"Failed to connect to {url}: {e}")
                continue

        logger.warning("All connectivity tests failed - assuming offline")
        return False
    
    def update_data_source_status(self, success: bool, error_msg: str = None):
        """Update the data source status in database"""
        try:
            with get_db_session() as session:
                source = session.query(DataSource).filter(
                    DataSource.source_name == self.source_name
                ).first()
                
                if not source:
                    source = DataSource(source_name=self.source_name)
                    session.add(source)
                
                source.last_check = datetime.utcnow()
                if success:
                    source.last_successful_update = datetime.utcnow()
                    source.error_count = 0
                    source.last_error = None
                else:
                    source.error_count += 1
                    source.last_error = error_msg
                
                session.commit()
        except Exception as e:
            logger.error(f"Failed to update data source status: {e}")
    
    def save_to_cache(self, data: dict, cache_key: str):
        """Save data to local cache for offline access"""
        try:
            cache_file = self.cache_dir / f"{self.source_name}_{cache_key}.json"
            with open(cache_file, 'w') as f:
                json.dump({
                    'timestamp': datetime.utcnow().isoformat(),
                    'data': data
                }, f, indent=2)
            logger.debug(f"Cached data to {cache_file}")
        except Exception as e:
            logger.error(f"Failed to cache data: {e}")
    
    def load_from_cache(self, cache_key: str, max_age_hours: int = 24) -> Optional[dict]:
        """Load data from cache if available and not too old"""
        try:
            cache_file = self.cache_dir / f"{self.source_name}_{cache_key}.json"
            if not cache_file.exists():
                return None
            
            with open(cache_file, 'r') as f:
                cached = json.load(f)
            
            # Check if cache is too old
            cache_time = datetime.fromisoformat(cached['timestamp'])
            if datetime.utcnow() - cache_time > timedelta(hours=max_age_hours):
                logger.debug(f"Cache expired for {cache_key}")
                return None
            
            logger.debug(f"Using cached data for {cache_key}")
            return cached['data']
        except Exception as e:
            logger.error(f"Failed to load cache: {e}")
            return None

class SECEdgarCollector(DataCollectorBase):
    """Collects IPO data from SEC EDGAR database"""
    
    def __init__(self):
        super().__init__("SEC_EDGAR")
        self.base_url = "https://www.sec.gov/Archives/edgar"
        # SEC requires proper User-Agent with contact info
        self.headers = {
            'User-Agent': 'IPO Tracker <NAME_EMAIL>',
            'Accept-Encoding': 'gzip, deflate',
            'Accept': 'application/json, text/html, application/xhtml+xml, application/xml;q=0.9, */*;q=0.8',
            'Connection': 'keep-alive'
        }
        # Use the new SEC EDGAR Data API instead of RSS
        self.api_base = "https://data.sec.gov"
    
    def fetch_recent_s1_filings(self, days_back: int = 7) -> List[IPOData]:
        """Fetch recent S-1 filings (IPO registrations)"""
        logger.info(f"Fetching S-1 filings from last {days_back} days")
        
        # Try to get fresh data if online
        if self.is_online():
            try:
                return self._fetch_fresh_s1_filings(days_back)
            except Exception as e:
                logger.error(f"Failed to fetch fresh S-1 data: {e}")
                self.update_data_source_status(False, str(e))
        
        # Fall back to cached data
        logger.warning("Using cached S-1 data due to connectivity issues")
        cached_data = self.load_from_cache("s1_filings", max_age_hours=48)
        if cached_data:
            return [IPOData(**item) for item in cached_data]
        
        logger.error("No cached S-1 data available")
        return []
    
    def _fetch_fresh_s1_filings(self, days_back: int) -> List[IPOData]:
        """Fetch fresh S-1 filings from SEC EDGAR"""
        logger.info("Fetching real S-1 filings from SEC EDGAR")

        ipos = []

        try:
            # Get recent filings from SEC EDGAR RSS feed
            recent_filings = self._get_recent_sec_filings(days_back)

            # Filter for S-1 filings (IPO registrations)
            s1_filings = [f for f in recent_filings if f.get('form_type') == 'S-1']
            logger.info(f"Found {len(s1_filings)} S-1 filings in last {days_back} days")

            for filing in s1_filings:
                try:
                    # Extract company data from filing
                    ipo_data = self._extract_company_data_from_filing(filing)
                    if ipo_data:
                        ipos.append(ipo_data)
                        logger.info(f"Extracted data for {ipo_data.company_name}")

                    # Rate limiting - SEC allows 10 requests per second
                    time.sleep(0.1)

                except Exception as e:
                    logger.error(f"Failed to process filing {filing.get('accession_number', 'unknown')}: {e}")
                    continue

            # Cache the results
            cache_data = []
            for ipo in ipos:
                cache_data.append({
                    'company_name': ipo.company_name,
                    'ticker_symbol': ipo.ticker_symbol,
                    'industry': ipo.industry,
                    'sector': ipo.sector,
                    'ceo_name': ipo.ceo_name,
                    'headquarters': ipo.headquarters,
                    'description': ipo.description,
                    'filing_date': ipo.filing_date.isoformat() if ipo.filing_date else None,
                    'exchange': ipo.exchange,
                    'sec_filing_url': ipo.sec_filing_url,
                    'cik_number': ipo.cik_number
                })

            self.save_to_cache(cache_data, "s1_filings")
            self.update_data_source_status(True)

            logger.info(f"Successfully processed {len(ipos)} S-1 filings")
            return ipos

        except Exception as e:
            logger.error(f"Failed to fetch S-1 filings: {e}")
            self.update_data_source_status(False, str(e))
            raise

    def _get_recent_sec_filings(self, days_back: int) -> List[Dict]:
        """Get recent filings using SEC EDGAR Data API"""
        filings = []

        try:
            # Use SEC's new data API for recent filings
            # This is more reliable than the RSS feed
            api_url = f"{self.api_base}/api/xbrl/companyfacts"

            # For now, let's use a different approach - get filings from submissions endpoint
            # We'll search for recent S-1 filings specifically

            # Alternative: Use a known list of recent IPO companies for demonstration
            # In production, you'd implement proper SEC API pagination

            logger.info("Using SEC EDGAR Data API for recent filings")

            # Check if mock data is enabled
            if not Config.USE_MOCK_DATA:
                logger.info("Mock data disabled - no real SEC API implementation yet")
                return []

            logger.warning("Using MOCK DATA - not real SEC filings!")
            cutoff_date = datetime.utcnow() - timedelta(days=days_back)

            # MOCK DATA - These are fictional companies for testing purposes only
            # In production, this would be replaced with real SEC API calls
            sample_companies = [
                {
                    'company_name': 'TechDemo Corp [MOCK DATA]',
                    'cik_number': '0009999001',
                    'form_type': 'S-1',
                    'filing_date': datetime.utcnow() - timedelta(days=2),
                    'filing_url': 'https://www.sec.gov/mock/demo/filing1.htm'
                },
                {
                    'company_name': 'CloudTest Inc [MOCK DATA]',
                    'cik_number': '0009999002',
                    'form_type': 'S-1',
                    'filing_date': datetime.utcnow() - timedelta(days=1),
                    'filing_url': 'https://www.sec.gov/mock/demo/filing2.htm'
                },
                {
                    'company_name': 'DataSample LLC [MOCK DATA]',
                    'cik_number': '0009999003',
                    'form_type': 'S-1',
                    'filing_date': datetime.utcnow() - timedelta(days=3),
                    'filing_url': 'https://www.sec.gov/mock/demo/filing3.htm'
                }
            ]

            # Filter by date and add to filings
            for company in sample_companies:
                if company['filing_date'] >= cutoff_date:
                    filings.append({
                        'form_type': company['form_type'],
                        'company_name': company['company_name'],
                        'cik_number': company['cik_number'],
                        'filing_date': company['filing_date'],
                        'filing_url': company['filing_url'],
                        'accession_number': self._extract_accession_from_url(company['filing_url'])
                    })

            logger.info(f"Found {len(filings)} recent S-1 filings")
            return filings

        except Exception as e:
            logger.error(f"Failed to fetch SEC filings: {e}")
            return []

    def _extract_accession_from_url(self, url: str) -> Optional[str]:
        """Extract accession number from SEC filing URL"""
        # URL format: https://www.sec.gov/Archives/edgar/data/1234567/000123456720000001/filename.htm
        match = re.search(r'/(\d{10}-\d{2}-\d{6})/', url)
        return match.group(1) if match else None

    def _extract_company_data_from_filing(self, filing: Dict) -> Optional[IPOData]:
        """Extract company data from SEC filing"""
        try:
            company_name = filing.get('company_name', '').strip()
            if not company_name:
                return None

            # Enhanced company data based on known IPO information
            company_data = self._get_enhanced_company_data(company_name, filing.get('cik_number'))

            return IPOData(
                company_name=company_name,
                ticker_symbol=company_data.get('ticker_symbol'),
                industry=company_data.get('industry'),
                sector=company_data.get('sector'),
                ceo_name=company_data.get('ceo_name'),
                headquarters=company_data.get('headquarters'),
                description=company_data.get('description'),
                filing_date=filing.get('filing_date'),
                exchange=company_data.get('exchange', 'NASDAQ'),  # Default to NASDAQ
                sec_filing_url=filing.get('filing_url'),
                cik_number=filing.get('cik_number')
            )

        except Exception as e:
            logger.error(f"Failed to extract company data: {e}")
            return None

    def _get_enhanced_company_data(self, company_name: str, cik: str) -> Dict:
        """Get enhanced company data based on known information"""
        # This would typically fetch from SEC filings or other sources
        # For now, we'll provide realistic data for known companies

        # MOCK DATA - Fictional company information for testing
        company_data_map = {
            'TechDemo Corp [MOCK DATA]': {
                'ticker_symbol': 'DEMO',
                'industry': 'Software',
                'sector': 'Technology',
                'ceo_name': 'John Demo',
                'headquarters': 'San Francisco, CA',
                'description': 'MOCK DATA: A fictional technology company for testing the IPO tracker system.',
                'exchange': 'NASDAQ'
            },
            'CloudTest Inc [MOCK DATA]': {
                'ticker_symbol': 'TEST',
                'industry': 'Cloud Services',
                'sector': 'Technology',
                'ceo_name': 'Jane Test',
                'headquarters': 'Seattle, WA',
                'description': 'MOCK DATA: A fictional cloud services company for testing purposes.',
                'exchange': 'NASDAQ'
            },
            'DataSample LLC [MOCK DATA]': {
                'ticker_symbol': 'SMPL',
                'industry': 'Data Analytics',
                'sector': 'Technology',
                'ceo_name': 'Bob Sample',
                'headquarters': 'Austin, TX',
                'description': 'MOCK DATA: A fictional data analytics company for system testing.',
                'exchange': 'NYSE'
            }
        }

        return company_data_map.get(company_name, {
            'industry': 'Technology',  # Default assumption
            'sector': 'Technology',
            'exchange': 'NASDAQ'
        })

    def _determine_exchange(self, filing_url: str) -> Optional[str]:
        """Determine target exchange from filing (simplified)"""
        # This is a placeholder - in reality, you'd parse the filing document
        # to find the intended exchange (NASDAQ, NYSE, etc.)

        # For now, we'll make an educated guess based on patterns
        # Most tech companies go to NASDAQ, traditional companies to NYSE
        return "NASDAQ"  # Default assumption

class AlphaVantageCollector(DataCollectorBase):
    """Collects company data from Alpha Vantage API"""
    
    def __init__(self):
        super().__init__("ALPHA_VANTAGE")
        self.api_key = Config.ALPHA_VANTAGE_API_KEY
        self.base_url = "https://www.alphavantage.co/query"
        self.rate_limit_delay = 12  # seconds between requests (5 per minute limit)
    
    def enrich_company_data(self, company_name: str, ticker: str = None) -> Dict:
        """Enrich company data using Alpha Vantage"""
        logger.info(f"Enriching data for {company_name} ({ticker})")
        
        if not self.api_key:
            logger.warning("Alpha Vantage API key not configured")
            return {}
        
        # Try fresh data if online
        if self.is_online():
            try:
                return self._fetch_fresh_company_data(company_name, ticker)
            except Exception as e:
                logger.error(f"Failed to fetch fresh company data: {e}")
                self.update_data_source_status(False, str(e))
        
        # Fall back to cache
        cache_key = f"company_{ticker or company_name.replace(' ', '_')}"
        cached_data = self.load_from_cache(cache_key, max_age_hours=72)
        return cached_data or {}
    
    def _fetch_fresh_company_data(self, company_name: str, ticker: str = None) -> Dict:
        """Fetch fresh company data from Alpha Vantage"""
        if not ticker:
            logger.warning(f"No ticker provided for {company_name}, skipping Alpha Vantage lookup")
            return {}
        
        try:
            # Company overview
            params = {
                'function': 'OVERVIEW',
                'symbol': ticker,
                'apikey': self.api_key
            }
            
            response = requests.get(self.base_url, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Check for API errors
            if 'Error Message' in data:
                raise Exception(f"Alpha Vantage error: {data['Error Message']}")
            
            if 'Note' in data:
                logger.warning(f"Alpha Vantage rate limit: {data['Note']}")
                time.sleep(60)  # Wait a minute if rate limited
                return {}
            
            # Extract useful information
            enriched_data = {
                'market_cap': data.get('MarketCapitalization'),
                'pe_ratio': data.get('PERatio'),
                'sector': data.get('Sector'),
                'industry': data.get('Industry'),
                'description': data.get('Description'),
                'address': data.get('Address'),
                'exchange': data.get('Exchange'),
                'country': data.get('Country'),
                'currency': data.get('Currency'),
                'fiscal_year_end': data.get('FiscalYearEnd'),
                'latest_quarter': data.get('LatestQuarter')
            }
            
            # Cache the results
            cache_key = f"company_{ticker}"
            self.save_to_cache(enriched_data, cache_key)
            self.update_data_source_status(True)
            
            # Rate limiting
            time.sleep(self.rate_limit_delay)
            
            return enriched_data
            
        except Exception as e:
            logger.error(f"Alpha Vantage API error: {e}")
            raise

class IPODataManager:
    """Manages the overall IPO data collection process"""
    
    def __init__(self):
        self.sec_collector = SECEdgarCollector()
        self.alpha_collector = AlphaVantageCollector()
    
    def collect_new_ipos(self) -> int:
        """Collect new IPO data and store in database"""
        logger.info("Starting IPO data collection")
        
        try:
            # Get recent S-1 filings
            new_ipos = self.sec_collector.fetch_recent_s1_filings()
            logger.info(f"Found {len(new_ipos)} potential new IPOs")
            
            saved_count = 0
            
            for ipo_data in new_ipos:
                try:
                    if self._save_ipo_to_database(ipo_data):
                        saved_count += 1
                except Exception as e:
                    logger.error(f"Failed to save IPO {ipo_data.company_name}: {e}")
            
            logger.info(f"Successfully saved {saved_count} new IPOs")
            return saved_count
            
        except Exception as e:
            logger.error(f"IPO collection failed: {e}")
            return 0
    
    def _save_ipo_to_database(self, ipo_data: IPOData) -> bool:
        """Save IPO data to database, avoiding duplicates"""
        with get_db_session() as session:
            # Check if company already exists
            existing_company = session.query(Company).filter(
                Company.name == ipo_data.company_name
            ).first()
            
            if existing_company:
                logger.debug(f"Company {ipo_data.company_name} already exists")
                return False
            
            # Create new company
            company = Company(
                name=ipo_data.company_name,
                ticker_symbol=ipo_data.ticker_symbol,
                industry=ipo_data.industry,
                sector=ipo_data.sector,
                ceo_name=ipo_data.ceo_name,
                founded_year=ipo_data.founded_year,
                headquarters=ipo_data.headquarters,
                website=ipo_data.website,
                description=ipo_data.description
            )
            session.add(company)
            session.flush()  # Get the company ID
            
            # Create IPO record
            ipo = IPO(
                company_id=company.id,
                filing_date=ipo_data.filing_date,
                expected_date=ipo_data.expected_date,
                price_range_low=ipo_data.price_range_low,
                price_range_high=ipo_data.price_range_high,
                shares_offered=ipo_data.shares_offered,
                exchange=ipo_data.exchange,
                sec_filing_url=ipo_data.sec_filing_url,
                cik_number=ipo_data.cik_number,
                status='filed'
            )
            session.add(ipo)
            
            session.commit()
            logger.info(f"Saved new IPO: {ipo_data.company_name}")
            return True
