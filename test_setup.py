"""
Test script to verify your IPO Tracker setup is working correctly.
This will test database connection, API keys, and the notes feature.
"""

import os
import sys
from config import Config
from database import db_manager, init_database
from models import Company, IPO
from notes_manager import NotesManager

def test_configuration():
    """Test that configuration is properly loaded"""
    print("🔧 Testing Configuration...")
    
    try:
        Config.validate()
        print("✅ Configuration loaded successfully")
        print(f"   OpenAI API Key: {'✓ Set' if Config.OPENAI_API_KEY else '✗ Missing'}")
        print(f"   Alpha Vantage Key: {'✓ Set' if Config.ALPHA_VANTAGE_API_KEY else '✗ Missing'}")
        return True
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_database():
    """Test database connection and setup"""
    print("\n🗄️ Testing Database...")
    
    try:
        # Test connection
        if not db_manager.test_connection():
            print("❌ Database connection failed")
            return False
        
        print("✅ Database connection successful")
        
        # Initialize database (create tables if they don't exist)
        init_database()
        print("✅ Database tables verified/created")
        
        # Get stats
        stats = db_manager.get_stats()
        print(f"   Companies: {stats.get('companies', 0)}")
        print(f"   IPOs: {stats.get('ipos', 0)}")
        print(f"   AI Reports: {stats.get('ai_reports', 0)}")
        
        return True
    except Exception as e:
        print(f"❌ Database error: {e}")
        return False

def test_api_keys():
    """Test API key functionality"""
    print("\n🔑 Testing API Keys...")
    
    # Test OpenAI API Key
    try:
        import openai
        openai.api_key = Config.OPENAI_API_KEY
        
        # Simple test call (this might use a small amount of credits)
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo",
            messages=[{"role": "user", "content": "Say 'API test successful'"}],
            max_tokens=10
        )
        print("✅ OpenAI API key working")
    except Exception as e:
        print(f"⚠️  OpenAI API test failed: {e}")
        print("   This might be due to rate limits or billing issues")
    
    # Test Alpha Vantage API Key
    try:
        import requests
        url = f"https://www.alphavantage.co/query?function=GLOBAL_QUOTE&symbol=AAPL&apikey={Config.ALPHA_VANTAGE_API_KEY}"
        response = requests.get(url, timeout=10)
        data = response.json()
        
        if "Error Message" in data or "Note" in data:
            print(f"⚠️  Alpha Vantage API issue: {data}")
        else:
            print("✅ Alpha Vantage API key working")
    except Exception as e:
        print(f"⚠️  Alpha Vantage API test failed: {e}")

def test_notes_feature():
    """Test the notes functionality with sample data"""
    print("\n📝 Testing Notes Feature...")
    
    try:
        from database import get_db_session
        
        # Create a sample company and IPO for testing
        with get_db_session() as session:
            # Check if test company already exists
            test_company = session.query(Company).filter(
                Company.name == "Test Company Inc."
            ).first()
            
            if not test_company:
                # Create test company
                test_company = Company(
                    name="Test Company Inc.",
                    ticker_symbol="TEST",
                    industry="Technology",
                    ceo_name="Test CEO",
                    description="A test company for demonstration purposes"
                )
                session.add(test_company)
                session.flush()  # Get the ID
                
                # Create test IPO
                test_ipo = IPO(
                    company_id=test_company.id,
                    status="filed",
                    exchange="NASDAQ"
                )
                session.add(test_ipo)
                session.commit()
                
                print("✅ Created test company and IPO")
            else:
                print("✅ Test company already exists")
        
        # Test notes manager
        notes_manager = NotesManager()
        
        # Add a test note
        success = notes_manager.add_note("Test Company", "This is a test note for the IPO tracker system.")
        if success:
            print("✅ Notes feature working - added test note")
        else:
            print("❌ Failed to add test note")
            return False
        
        # View the note
        print("\n📋 Test note content:")
        notes_manager.view_notes("Test Company")
        
        return True
        
    except Exception as e:
        print(f"❌ Notes feature test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 IPO TRACKER SETUP TEST")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Database", test_database),
        ("API Keys", test_api_keys),
        ("Notes Feature", test_notes_feature),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("\n🎉 All tests passed! Your IPO Tracker is ready to use.")
        print("\nNext steps:")
        print("1. Run 'python main.py' to start the application")
        print("2. Use option 5 to manage IPO notes")
        print("3. The system is ready for IPO data collection development")
    else:
        print(f"\n⚠️  {len(results) - passed} test(s) failed. Please check the errors above.")
        print("Common issues:")
        print("- Make sure PostgreSQL is running")
        print("- Check your .env file has correct database credentials")
        print("- Verify your API keys are valid")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
