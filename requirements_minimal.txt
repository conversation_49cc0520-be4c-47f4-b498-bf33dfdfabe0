# Minimal requirements for testing (no PostgreSQL needed)
sqlalchemy==2.0.21          # Database ORM (works with SQLite)
requests==2.31.0            # HTTP requests for APIs
python-dateutil==2.8.2     # Date parsing

# These are optional for basic testing
# APScheduler==3.10.4         # Background scheduling (we'll add later)
# openai==0.28.1              # AI reports (we'll add later)
# beautifulsoup4==4.12.2      # Web scraping (we'll add later)
# pandas==2.1.1               # Data manipulation (we'll add later)
