# Core dependencies
psycopg2-binary==2.9.7      # PostgreSQL database adapter
sqlalchemy==2.0.21          # Database ORM (Object Relational Mapping)
alembic==1.12.0             # Database migrations

# Data fetching and processing
requests==2.31.0            # HTTP requests for APIs
beautifulsoup4==4.12.2      # Web scraping
pandas==2.1.1               # Data manipulation
python-dateutil==2.8.2     # Date parsing

# Background task scheduling
APScheduler==3.10.4         # Advanced Python Scheduler

# AI integration
openai==0.28.1              # OpenAI API for AI reports

# Web interface (optional - we'll start with CLI)
flask==2.3.3                # Web framework
flask-cors==4.0.0           # Cross-origin requests

# Configuration and environment
python-dotenv==1.0.0        # Environment variables
pydantic==2.4.2             # Data validation

# Logging and utilities
loguru==0.7.2               # Better logging
click==8.1.7                # Command line interface
