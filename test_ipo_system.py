"""
Test script for the IPO collection system
"""

import sys
from datetime import datetime
from database import get_db_session, init_database
from models import Company, IPO
from data_collectors import IPODataManager

def test_ipo_collection():
    """Test the IPO data collection system"""
    print("🧪 Testing IPO Collection System")
    print("=" * 50)
    
    try:
        # Initialize database
        print("🔄 Initializing database...")
        init_database()
        print("✅ Database initialized")
        
        # Test data collection
        print("\n🔄 Testing IPO data collection...")
        data_manager = IPODataManager()
        new_count = data_manager.collect_new_ipos()
        
        print(f"✅ Collection completed: {new_count} new IPOs")
        
        # Show what we collected
        with get_db_session() as session:
            companies = session.query(Company).all()
            ipos = session.query(IPO).all()
            
            print(f"\n📊 Database contents:")
            print(f"   Companies: {len(companies)}")
            print(f"   IPOs: {len(ipos)}")
            
            if companies:
                print(f"\n📋 Companies found:")
                for company in companies:
                    print(f"   🏢 {company.name}")
                    if company.ticker_symbol:
                        print(f"      Ticker: {company.ticker_symbol}")
                    if company.industry:
                        print(f"      Industry: {company.industry}")
                    if company.ceo_name:
                        print(f"      CEO: {company.ceo_name}")
                    
                    # Show IPO info
                    ipo = session.query(IPO).filter(IPO.company_id == company.id).first()
                    if ipo:
                        print(f"      IPO Status: {ipo.status}")
                        if ipo.filing_date:
                            print(f"      Filed: {ipo.filing_date.strftime('%Y-%m-%d')}")
                        if ipo.exchange:
                            print(f"      Exchange: {ipo.exchange}")
                    print()
        
        print("🎉 IPO collection system test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_notes_with_ipo():
    """Test adding notes to collected IPOs"""
    print("\n🧪 Testing Notes with IPOs")
    print("=" * 30)
    
    try:
        from notes_manager import NotesManager
        notes_manager = NotesManager()
        
        # Find a company to add notes to
        with get_db_session() as session:
            company = session.query(Company).first()
            
            if not company:
                print("❌ No companies found to test notes with")
                return False
            
            # Add a test note
            success = notes_manager.add_note(
                company.name, 
                "This is a test note added by the IPO collection system test."
            )
            
            if success:
                print(f"✅ Successfully added note to {company.name}")
                
                # View the note
                print("\n📝 Note content:")
                notes_manager.view_notes(company.name)
                return True
            else:
                print("❌ Failed to add note")
                return False
                
    except Exception as e:
        print(f"❌ Notes test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 IPO TRACKER SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        ("IPO Collection", test_ipo_collection),
        ("Notes Integration", test_notes_with_ipo),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: CRASHED - {e}")
    
    print(f"\n📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 All tests passed! Your IPO tracking system is working!")
        print("\nNext steps:")
        print("1. Run 'python main.py' to use the interactive menu")
        print("2. Try option 6 to collect IPO data")
        print("3. Try option 7 to start the background scheduler")
        print("4. Use option 5 to add notes to interesting IPOs")
    else:
        print(f"\n⚠️  {len(tests) - passed} test(s) failed")
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
