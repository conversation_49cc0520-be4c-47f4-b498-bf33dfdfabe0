"""
Database reset script - drops and recreates the IPO tracker database
"""

import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from config import Config
import sys
from loguru import logger

def reset_database():
    """Drop and recreate the IPO tracker database"""
    
    # Parse the database URL to get connection details
    db_url = Config.DATABASE_URL
    logger.info(f"Resetting database from URL: {db_url}")
    
    # Extract connection details
    if "postgresql://" in db_url:
        parts = db_url.replace("postgresql://", "").split("/")
        user_host_port = parts[0]
        database_name = parts[1] if len(parts) > 1 else "ipo_tracker"
        
        if "@" in user_host_port:
            user_pass, host_port = user_host_port.split("@")
            if ":" in user_pass:
                user, password = user_pass.split(":", 1)
            else:
                user = user_pass
                password = None
        else:
            user = "zhouchaoran"
            password = None
            host_port = user_host_port
        
        if ":" in host_port:
            host, port = host_port.split(":")
        else:
            host = host_port
            port = "5432"
    else:
        logger.error("Invalid database URL format")
        return False
    
    try:
        # Connect to the default 'postgres' database
        logger.info("Connecting to PostgreSQL...")
        conn_params = {
            "host": host,
            "port": port,
            "user": user,
            "database": "postgres"
        }
        if password:
            conn_params["password"] = password
            
        conn = psycopg2.connect(**conn_params)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        logger.info("Connected successfully!")
        
        # Drop the database if it exists
        logger.info(f"Dropping database '{database_name}' if it exists...")
        cursor.execute(f'DROP DATABASE IF EXISTS "{database_name}"')
        logger.info(f"Database '{database_name}' dropped")
        
        # Create the database
        logger.info(f"Creating fresh database '{database_name}'...")
        cursor.execute(f'CREATE DATABASE "{database_name}"')
        logger.info(f"Database '{database_name}' created successfully!")
        
        cursor.close()
        conn.close()
        
        # Test connection to new database
        logger.info(f"Testing connection to new database...")
        test_conn_params = {
            "host": host,
            "port": port,
            "user": user,
            "database": database_name
        }
        if password:
            test_conn_params["password"] = password
            
        test_conn = psycopg2.connect(**test_conn_params)
        test_cursor = test_conn.cursor()
        test_cursor.execute("SELECT version();")
        version = test_cursor.fetchone()
        logger.info(f"New database ready! PostgreSQL version: {version[0]}")
        
        test_cursor.close()
        test_conn.close()
        
        return True
        
    except psycopg2.Error as e:
        logger.error(f"PostgreSQL error: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return False

def main():
    """Main function"""
    print("🗄️ IPO TRACKER DATABASE RESET")
    print("=" * 50)
    
    # Confirm with user
    confirm = input("⚠️  This will DELETE ALL DATA in the IPO tracker database. Continue? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ Database reset cancelled")
        return False
    
    success = reset_database()
    
    if success:
        print("\n🎉 Database reset completed successfully!")
        print("Next steps:")
        print("1. Run: python database.py (to create tables)")
        print("2. Run: python test_setup.py (to verify setup)")
        print("3. Run: python main.py (to start using the system)")
    else:
        print("\n❌ Database reset failed. Please check the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
