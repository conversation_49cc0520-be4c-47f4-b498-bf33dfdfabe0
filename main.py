"""
Main application entry point for the IPO Tracker.
This is where everything comes together.
"""

import sys
from pathlib import Path
from loguru import logger
from config import Config
from database import init_database, db_manager

def setup_logging():
    """Configure logging for the application"""
    # Remove default logger
    logger.remove()
    
    # Add console logging
    logger.add(
        sys.stdout,
        level=Config.LOG_LEVEL,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Add file logging
    log_file = Config.LOGS_DIR / "ipo_tracker.log"
    logger.add(
        log_file,
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="30 days"
    )
    
    logger.info("Logging configured successfully")

def check_configuration():
    """Check if the application is properly configured"""
    logger.info("Checking application configuration...")
    
    try:
        Config.validate()
        logger.info("✅ Configuration validation passed")
        return True
    except ValueError as e:
        logger.error(f"❌ Configuration validation failed: {e}")
        logger.error("Please check your .env file and ensure all required variables are set")
        return False

def initialize_application():
    """Initialize the application"""
    logger.info("Initializing IPO Tracker application...")
    
    # Setup logging
    setup_logging()
    
    # Check configuration
    if not check_configuration():
        return False
    
    # Initialize database
    try:
        init_database()
        logger.info("✅ Database initialized successfully")
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        return False
    
    logger.info("🎉 Application initialized successfully!")
    return True

def show_status():
    """Show current application status"""
    print("\n" + "="*50)
    print("📊 IPO TRACKER STATUS")
    print("="*50)
    
    # Database stats
    try:
        stats = db_manager.get_stats()
        print(f"📈 Companies tracked: {stats.get('companies', 0)}")
        print(f"📋 IPO filings: {stats.get('ipos', 0)}")
        print(f"🤖 AI reports generated: {stats.get('ai_reports', 0)}")
    except Exception as e:
        print(f"❌ Could not get database stats: {e}")
    
    # Configuration status
    print(f"\n⚙️  Configuration:")
    print(f"   Database: {'✅ Connected' if db_manager.test_connection() else '❌ Not connected'}")
    print(f"   OpenAI API: {'✅ Configured' if Config.OPENAI_API_KEY else '❌ Missing'}")
    print(f"   Log level: {Config.LOG_LEVEL}")
    
    print("\n" + "="*50)

def main_menu():
    """Show the main application menu"""
    while True:
        print("\n🚀 IPO TRACKER - Main Menu")
        print("-" * 30)
        print("1. Show Status")
        print("2. Initialize Database")
        print("3. Test Database Connection")
        print("4. View Configuration")
        print("5. Manage IPO Notes")
        print("6. Exit")

        choice = input("\nSelect an option (1-6): ").strip()
        
        if choice == "1":
            show_status()
        elif choice == "2":
            try:
                init_database()
                print("✅ Database initialized successfully!")
            except Exception as e:
                print(f"❌ Database initialization failed: {e}")
        elif choice == "3":
            if db_manager.test_connection():
                print("✅ Database connection successful!")
            else:
                print("❌ Database connection failed!")
        elif choice == "4":
            print(f"\n⚙️  Current Configuration:")
            print(f"   Database URL: {Config.DATABASE_URL}")
            print(f"   OpenAI API Key: {'Set' if Config.OPENAI_API_KEY else 'Not set'}")
            print(f"   Log Level: {Config.LOG_LEVEL}")
            print(f"   Scheduler Interval: {Config.SCHEDULER_INTERVAL_HOURS} hours")
        elif choice == "5":
            # Launch notes manager
            try:
                from notes_manager import main as notes_main
                notes_main()
            except Exception as e:
                print(f"❌ Failed to launch notes manager: {e}")
        elif choice == "6":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid option. Please try again.")

def main():
    """Main application function"""
    print("🚀 Starting IPO Tracker...")
    
    # Initialize application
    if not initialize_application():
        print("❌ Application initialization failed. Please check the logs.")
        return 1
    
    # Show initial status
    show_status()
    
    # Start main menu
    try:
        main_menu()
    except KeyboardInterrupt:
        print("\n\n👋 Application stopped by user")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        print(f"❌ Unexpected error: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
